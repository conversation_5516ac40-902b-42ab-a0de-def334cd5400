#!/bin/bash

# 前端项目 Docker 构建脚本
# 支持多架构构建和错误处理

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    log_info "Docker 版本: $(docker --version)"
}

# 检查项目结构
check_project_structure() {
    if [ ! -f "../frontend/package.json" ]; then
        log_error "未找到 frontend/package.json 文件"
        exit 1
    fi
    
    if [ ! -f "nginx.conf" ]; then
        log_error "未找到 nginx.conf 文件"
        exit 1
    fi
    
    log_info "项目结构检查通过"
}

# 清理旧的构建
cleanup() {
    log_info "清理旧的 Docker 镜像和容器..."
    
    # 停止并删除旧容器
    if docker ps -a | grep -q "codebase-frontend"; then
        docker stop codebase-frontend 2>/dev/null || true
        docker rm codebase-frontend 2>/dev/null || true
        log_info "已清理旧容器"
    fi
    
    # 删除旧镜像
    if docker images | grep -q "codebase-frontend"; then
        docker rmi codebase-frontend 2>/dev/null || true
        log_info "已清理旧镜像"
    fi
}

# 构建 Docker 镜像
build_image() {
    local dockerfile=${1:-"frontend.dockerfile"}
    local tag=${2:-"codebase-frontend"}
    
    log_info "开始构建 Docker 镜像..."
    log_info "使用 Dockerfile: $dockerfile"
    log_info "镜像标签: $tag"
    
    # 检测当前架构
    local arch=$(uname -m)
    log_info "当前架构: $arch"
    
    # 构建镜像
    if docker build \
        --platform linux/amd64,linux/arm64 \
        -f "$dockerfile" \
        -t "$tag" \
        --progress=plain \
        ..; then
        log_success "Docker 镜像构建成功"
    else
        log_error "Docker 镜像构建失败"
        exit 1
    fi
}

# 运行容器
run_container() {
    local tag=${1:-"codebase-frontend"}
    local port=${2:-"3000"}
    
    log_info "启动 Docker 容器..."
    
    if docker run -d \
        --name codebase-frontend \
        -p "$port:80" \
        --restart unless-stopped \
        --health-cmd="curl -f http://localhost/health || exit 1" \
        --health-interval=30s \
        --health-timeout=3s \
        --health-retries=3 \
        "$tag"; then
        log_success "容器启动成功"
        log_info "访问地址: http://localhost:$port"
    else
        log_error "容器启动失败"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "前端项目 Docker 构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -c, --cleanup       仅清理旧的镜像和容器"
    echo "  -b, --build-only    仅构建镜像，不运行容器"
    echo "  -r, --run-only      仅运行容器（假设镜像已存在）"
    echo "  -p, --port PORT     指定端口（默认: 3000）"
    echo "  -t, --tag TAG       指定镜像标签（默认: codebase-frontend）"
    echo "  -f, --file FILE     指定 Dockerfile（默认: frontend.dockerfile）"
    echo ""
    echo "示例:"
    echo "  $0                  # 完整构建和运行"
    echo "  $0 -p 8080          # 使用端口 8080"
    echo "  $0 -b               # 仅构建镜像"
    echo "  $0 -c               # 仅清理"
}

# 主函数
main() {
    local dockerfile="frontend.dockerfile"
    local tag="codebase-frontend"
    local port="3000"
    local cleanup_only=false
    local build_only=false
    local run_only=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--cleanup)
                cleanup_only=true
                shift
                ;;
            -b|--build-only)
                build_only=true
                shift
                ;;
            -r|--run-only)
                run_only=true
                shift
                ;;
            -p|--port)
                port="$2"
                shift 2
                ;;
            -t|--tag)
                tag="$2"
                shift 2
                ;;
            -f|--file)
                dockerfile="$2"
                shift 2
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查环境
    check_docker
    check_project_structure
    
    # 执行操作
    if [ "$cleanup_only" = true ]; then
        cleanup
        log_success "清理完成"
        exit 0
    fi
    
    if [ "$run_only" = false ]; then
        cleanup
        build_image "$dockerfile" "$tag"
    fi
    
    if [ "$build_only" = false ]; then
        run_container "$tag" "$port"
        
        # 等待容器启动
        log_info "等待容器启动..."
        sleep 5
        
        # 检查容器状态
        if docker ps | grep -q "codebase-frontend"; then
            log_success "部署完成！"
            log_info "容器状态: $(docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}' | grep codebase-frontend)"
            log_info "查看日志: docker logs -f codebase-frontend"
            log_info "停止容器: docker stop codebase-frontend"
        else
            log_error "容器启动失败，请检查日志: docker logs codebase-frontend"
            exit 1
        fi
    fi
}

# 运行主函数
main "$@"
