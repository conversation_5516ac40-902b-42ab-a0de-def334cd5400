version: '3.8'

services:
  frontend:
    build:
      context: ..
      dockerfile: deploy/frontend.dockerfile
      platforms:
        - linux/amd64
        - linux/arm64
      args:
        - NODE_ENV=production
    container_name: codebase-frontend
    ports:
      - "3000:80"
    environment:
      - TZ=Asia/Shanghai
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - codebase-network
    volumes:
      - nginx-logs:/var/log/nginx

networks:
  codebase-network:
    driver: bridge

volumes:
  nginx-logs:
    driver: local
