import os
import json
import math
from typing import List
import rich
from collections import Counter
from functools import lru_cache

from modules.common.schema import CodeSnippet
from modules.common.search_tool import SearchToolABC
from utils.file import build_file_list
from utils.term import get_terms


class BM25Search(SearchToolABC):
    def __init__(self, repo_path: str):
        self.repo_path = repo_path
    
    @lru_cache()
    def load_cache_file(self, cache_file: str):
        with open(cache_file, 'r', encoding='utf-8') as f:
            return json.load(f)

    def load_context_files(self, project_dir: str, cache_dir: str, project_id: str):
        """
        加载上下文文件并构建BM25索引
        """
        cache_file = os.path.join(cache_dir, f"{project_id}.json")

        # 如果缓存文件存在，直接加载
        if os.path.exists(cache_file):
            cache_data = self.load_cache_file(cache_file)
            self.doc_content = cache_data['doc_content']
            self.term_idf = cache_data['term_idf']
            self.doc_term_freqs = cache_data['doc_term_freqs']
            self.doc_lengths = cache_data['doc_lengths']
            self.avg_doc_length = cache_data['avg_doc_length']
            return {"status": "loaded_from_cache", "documents": len(self.doc_content)}

        # 确保缓存目录存在
        os.makedirs(cache_dir, exist_ok=True)

        # 读取项目目录中的文件
        self.doc_content = build_file_list(project_dir)

        # 计算所有文件中的term和IDF值
        all_terms = set()
        doc_terms = {}
        doc_freqs = {}
        for file_path, content in rich.progress.track(self.doc_content.items(), description="Processing files..."):
            terms = get_terms(content)
            doc_terms[file_path] = terms
            all_terms.update(terms)
            for term in terms:
                doc_freqs[term] = doc_freqs.get(term, 0) + 1

        # 计算每个term的IDF值
        total_docs = len(self.doc_content)
        self.term_idf = {}

        for term in rich.progress.track(all_terms, description="Calculating IDF..."):
            doc_freq = doc_freqs.get(term, 0)
            # IDF计算：log(N/df)，其中N是总文档数，df是包含该term的文档数
            self.term_idf[term] = math.log(total_docs / doc_freq) if doc_freq > 0 else 0

        # 计算每个文件内每个term的频率
        self.doc_term_freqs = {}
        for file_path, terms in rich.progress.track(doc_terms.items(), description="Calculating term frequencies..."):
            term_counts = Counter(terms)
            self.doc_term_freqs[file_path] = dict(term_counts)


        # 计算平均文档长度
        self.doc_lengths = {file_path: sum(freqs.values())
                      for file_path, freqs in rich.progress.track(self.doc_term_freqs.items(), description="Calculating document lengths...")}
        self.avg_doc_length = sum(self.doc_lengths.values()) / len(self.doc_lengths) if self.doc_lengths else 1

        # 缓存结果
        cache_data = {
            'doc_content': self.doc_content,
            'term_idf': self.term_idf,
            'doc_term_freqs': self.doc_term_freqs,
            'doc_lengths': self.doc_lengths,
            'avg_doc_length': self.avg_doc_length
        }

        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, ensure_ascii=False, indent=2)

        return {
            "status": "indexed",
            "documents": len(self.doc_content),
            "unique_terms": len(self.term_idf)
        }

    def search(self, query: str, top_k: int) -> List[CodeSnippet]:
        if not self.doc_content or not self.term_idf or not self.doc_term_freqs:
            return {}

        # 提取查询词
        query_terms = get_terms(query)
        if not query_terms:
            return {}

        
        # 计算每个文档的BM25分数
        scores = {}
        for file_path in self.doc_content:
            score = self._calculate_bm25_score(
                query_terms, file_path, self.doc_lengths[file_path], self.avg_doc_length
            )
            if score > 0:
                scores[file_path] = score

        # 按分数排序并返回top_k结果
        sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        return dict(sorted_scores[:top_k])

    def _calculate_bm25_score(self, query_terms: List[str], file_path: str,
                             doc_length: int, avg_doc_length: float) -> float:
        """
        计算单个文档的BM25分数
        """
        if file_path not in self.doc_term_freqs:
            return 0.0

        term_freqs = self.doc_term_freqs[file_path]
        score = 0.0

        for term in query_terms:
            if term in term_freqs and term in self.term_idf:
                # 词频
                tf = term_freqs[term]
                # IDF值
                idf = self.term_idf[term]

                # BM25公式
                numerator = tf * (self.k1 + 1)
                denominator = tf + self.k1 * (1 - self.b + self.b * (doc_length / avg_doc_length))

                score += idf * (numerator / denominator)

        return score