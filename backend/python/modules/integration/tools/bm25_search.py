from typing import List

from modules.common.schema import CodeSnippet
from modules.common.search_tool import SearchToolABC

class BM25Search(SearchToolABC):
    def __init__(self, repo_path: str):
        self.repo_path = repo_path
        # 查询本地是否有当前repo的BM25索引，如果没有则创建
    
    def load_context_files(self, project_dir: str, cache_dir: str, project_id: str) -> Any:
        """
        加载上下文文件并构建BM25索引
        """
        cache_file = os.path.join(cache_dir, f"{project_id}.json")

        # 如果缓存文件存在，直接加载
        if os.path.exists(cache_file):
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
                self.doc_content = cache_data['doc_content']
                self.term_idf = cache_data['term_idf']
                self.doc_term_freqs = cache_data['doc_term_freqs']
                self.doc_lengths = cache_data['doc_lengths']
                self.avg_doc_length = cache_data['avg_doc_length']
                return {"status": "loaded_from_cache", "documents": len(self.doc_content)}

        # 确保缓存目录存在
        os.makedirs(cache_dir, exist_ok=True)

        # 读取项目目录中的文件
        self.doc_content = FileUtils.read_repo_dir(project_dir)

        # 计算所有文件中的term和IDF值
        all_terms = set()
        doc_terms = {}
        doc_freqs = {}
        for file_path, content in rich.progress.track(self.doc_content.items(), description="Processing files..."):
            terms = TermUtils.get_terms(content)
            doc_terms[file_path] = terms
            all_terms.update(terms)
            for term in terms:
                doc_freqs[term] = doc_freqs.get(term, 0) + 1

        # 计算每个term的IDF值
        total_docs = len(self.doc_content)
        self.term_idf = {}

        for term in rich.progress.track(all_terms, description="Calculating IDF..."):
            doc_freq = doc_freqs.get(term, 0)
            # IDF计算：log(N/df)，其中N是总文档数，df是包含该term的文档数
            self.term_idf[term] = math.log(total_docs / doc_freq) if doc_freq > 0 else 0

        # 计算每个文件内每个term的频率
        self.doc_term_freqs = {}
        for file_path, terms in rich.progress.track(doc_terms.items(), description="Calculating term frequencies..."):
            term_counts = Counter(terms)
            self.doc_term_freqs[file_path] = dict(term_counts)


        # 计算平均文档长度
        self.doc_lengths = {file_path: sum(freqs.values())
                      for file_path, freqs in rich.progress.track(self.doc_term_freqs.items(), description="Calculating document lengths...")}
        self.avg_doc_length = sum(self.doc_lengths.values()) / len(self.doc_lengths) if self.doc_lengths else 1

        # 缓存结果
        cache_data = {
            'doc_content': self.doc_content,
            'term_idf': self.term_idf,
            'doc_term_freqs': self.doc_term_freqs,
            'doc_lengths': self.doc_lengths,
            'avg_doc_length': self.avg_doc_length
        }

        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, ensure_ascii=False, indent=2)

        return {
            "status": "indexed",
            "documents": len(self.doc_content),
            "unique_terms": len(self.term_idf)
        }

    def search(self, query: str) -> List[CodeSnippet]:
        pass