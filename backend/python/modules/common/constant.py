from enum import Enum

class SearchToolEnum(Enum):
    GREP = "grep"
    EMBEDDING = "embedding"
    TDIDF = "tfidf"
    BM25 = "bm25"


    @property
    def description(self):
        if self == SearchToolEnum.GREP:
            return """Grep: use command to match the keywords in code snippet, the query input should be specific keywords and searchable。
- Expceted Query
1. keyword that likely appear in code snippets, like function, class, api name, etc 
2. keyword can be directly found in the code or documents
3. Just only one word

- Examples
Original Query: "解释这个存储级别的仓库的主要功能"
Output: ["Repository", "Storage", "Store", "Save", "Load", "Fetch", "Persist", "Read", "Write", "DataSource"]
"""
        elif self == SearchToolEnum.EMBEDDING:
            return "embedding: use vector database to search code, the query input should be general and cover all possible answers"
        else:
            return "未知搜索工具"

    @property
    def search_class(self):
        if self == SearchToolEnum.GREP:
            from backend.python.modules.integration.tools.grep_search import GrepSearchTool
            return GrepSearchTool
        elif self == SearchToolEnum.EMBEDDING:
            raise NotImplementedError("Embedding搜索暂未实现")
            # from modules.integration.embedding import EmbeddingSearchTool
            # return EmbeddingSearchTool
        else:
            raise ValueError("未知搜索工具")
